package com.cnpc.xn.cd.dataclean.service;

import com.alibaba.fastjson.JSON;
import com.alibaba.fastjson.JSONArray;
import com.alibaba.fastjson.JSONObject;
import com.baomidou.mybatisplus.core.conditions.query.QueryWrapper;
import com.cnpc.xn.cd.dataclean.config.DataCollectionConfig;
import com.cnpc.xn.cd.dataclean.entity.PointConfig;
import com.cnpc.xn.cd.dataclean.entity.PointData;
import com.cnpc.xn.cd.dataclean.mapper.PointConfigMapper;
import lombok.extern.slf4j.Slf4j;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.data.redis.core.RedisTemplate;
import org.springframework.stereotype.Service;

import javax.annotation.PostConstruct;
import javax.annotation.PreDestroy;
import java.util.List;
import java.util.Set;
import java.util.concurrent.Executors;
import java.util.concurrent.ScheduledExecutorService;
import java.util.concurrent.TimeUnit;
import java.util.stream.Collectors;

/**
 * PackName: com.cnpc.xn.cd.dataclean.service
 * ClassName: RedisService
 * Author:  zzl.F3
 * Date:     2025/5/28 10:57
 * Description: redis操作服务
 */
@Service
@Slf4j
public class RedisService {

    @Autowired
    private PointConfigMapper pointConfigMapper;

    @Autowired
    private RedisTemplate<String, String> redisTemplate;

    @Autowired
    private DataCollectionConfig collectionConfig;

    @Autowired
    private DataCleanService dataCleanService;

    // 线程池实例变量，用于管理调度任务
    private ScheduledExecutorService scheduler;
    private ScheduledExecutorService cleanScheduler;

    /**
     * Spring容器初始化完成后自动启动数据采集任务
     */
    @PostConstruct
    public void initDataCollection() {
        // 检查是否启用自动启动
        if (!collectionConfig.isAutoStart()) {
            log.info("数据采集自动启动已禁用，跳过启动");
            return;
        }

        // 延迟启动，确保所有依赖都已初始化完成
        new Thread(() -> {
            try {
                Thread.sleep(collectionConfig.getStartDelay() * 1000L); // 使用配置的延迟时间
                log.info("项目启动完成，开始自动启动数据采集任务，延迟{}秒", collectionConfig.getStartDelay());
                saveData();
            } catch (InterruptedException e) {
                log.error("数据采集任务启动被中断", e);
                Thread.currentThread().interrupt();
            } catch (Exception e) {
                log.error("自动启动数据采集任务失败", e);
            }
        }, "DataCollection-Initializer").start();
    }


    public void saveData() {
        // 如果已有线程池在运行，先关闭
        if (scheduler != null && !scheduler.isShutdown()) {
            log.info("关闭现有的数据采集线程池");
            stopDataCollection();
        }

        // 从数据库获取ID列表
        QueryWrapper<PointConfig> queryWrapper = new QueryWrapper<>();
        List<PointConfig> configs = pointConfigMapper.selectList(queryWrapper);
        List<String> idList = configs.stream()
                .map(PointConfig::getId)
                .collect(Collectors.toList());

        // 创建调度线程池（使用配置的线程数倍数）
        int corePoolSize = Runtime.getRuntime().availableProcessors() * collectionConfig.getThreadPoolMultiplier();
        scheduler = Executors.newScheduledThreadPool(corePoolSize, r -> {
            Thread thread = new Thread(r, "DataCollector-" + System.currentTimeMillis());
            thread.setDaemon(true); // 设置为守护线程
            return thread;
        });

        log.info("启动数据采集任务，点位数量：{}，线程池大小：{}", idList.size(), corePoolSize);

        // 分散调度任务
        idList.forEach(id -> {
            long initialDelay = (long) (Math.random() * 1000);

            scheduler.scheduleAtFixedRate(() -> {
                try {
                    String jsonData = dataCleanService.queryData(id);
                    // 使用配置的Redis缓存过期时间
                    redisTemplate.opsForValue().set("PSpaceQuery:" + id, jsonData,
                            collectionConfig.getCacheExpireTime(), TimeUnit.SECONDS);
                    log.debug("成功采集并缓存点位数据，ID：{}", id);
                } catch (Exception e) {
                    log.warn("采集点位数据失败，ID：{}，错误：{}", id, e.getMessage());
                }
            }, initialDelay, collectionConfig.getInterval(), TimeUnit.MILLISECONDS);
        });
    }


    /**
     * 停止数据采集任务
     */
    public void stopDataCollection() {
        log.info("手动停止数据采集任务");
        if (scheduler != null && !scheduler.isShutdown()) {
            scheduler.shutdown();
            try {
                if (!scheduler.awaitTermination(30, TimeUnit.SECONDS)) {
                    scheduler.shutdownNow();
                }
                log.info("数据采集任务已停止");
            } catch (InterruptedException e) {
                scheduler.shutdownNow();
                Thread.currentThread().interrupt();
            }
        }
    }

    /**
     * Spring容器销毁时自动调用，确保线程池正常关闭
     */
    @PreDestroy
    public void destroy() {
        log.info("Spring容器销毁，关闭数据采集线程池");
        stopDataCollection();
    }

    /**
     * 启动数据清洗任务
     * 每30秒从Redis读取数据并进行清洗，完成后移除数据
     */
    public void startDataCleanTask() {
        // 如果已有清洗任务在运行，先关闭
        if (cleanScheduler != null && !cleanScheduler.isShutdown()) {
            log.info("关闭现有的数据清洗线程池");
            cleanScheduler.shutdown();
            try {
                if (!cleanScheduler.awaitTermination(10, TimeUnit.SECONDS)) {
                    cleanScheduler.shutdownNow();
                }
            } catch (InterruptedException e) {
                cleanScheduler.shutdownNow();
                Thread.currentThread().interrupt();
            }
        }

        // 创建数据清洗线程池
        cleanScheduler = Executors.newScheduledThreadPool(2, r -> {
            Thread thread = new Thread(r, "DataCleaner-" + System.currentTimeMillis());
            thread.setDaemon(true);
            return thread;
        });

        log.info("启动数据清洗任务，每30秒执行一次");

        // 每30秒执行一次数据清洗任务
        cleanScheduler.scheduleAtFixedRate(() -> {
            try {
                processRedisDataAndClean();
            } catch (Exception e) {
                log.error("数据清洗任务执行失败", e);
            }
        }, 30, 30, TimeUnit.SECONDS); // 延迟30秒启动，然后每30秒执行一次
    }

    /**
     * 停止数据清洗任务
     */
    public void stopDataCleanTask() {
        log.info("停止数据清洗任务");
        if (cleanScheduler != null && !cleanScheduler.isShutdown()) {
            cleanScheduler.shutdown();
            try {
                if (!cleanScheduler.awaitTermination(30, TimeUnit.SECONDS)) {
                    cleanScheduler.shutdownNow();
                }
                log.info("数据清洗任务已停止");
            } catch (InterruptedException e) {
                cleanScheduler.shutdownNow();
                Thread.currentThread().interrupt();
            }
        }
    }

    /**
     * 从Redis读取数据并进行清洗处理
     */
    private void processRedisDataAndClean() {
        try {
            // 获取所有以"PSpaceQuery:"开头的Redis键
            Set<String> keys = redisTemplate.keys("PSpaceQuery:*");
            if (keys == null || keys.isEmpty()) {
                log.debug("Redis中没有找到待清洗的数据");
                return;
            }

            log.info("开始处理Redis数据，共{}个键", keys.size());
            int processedCount = 0;
            int successCount = 0;

            for (String key : keys) {
                try {
                    // 从Redis获取JSON数据
                    String jsonData = redisTemplate.opsForValue().get(key);
                    if (jsonData == null || jsonData.trim().isEmpty()) {
                        log.warn("Redis键{}对应的数据为空，跳过处理", key);
                        continue;
                    }

                    // 解析JSON数据并进行清洗
                    boolean cleanResult = parseAndCleanData(jsonData, key);

                    if (cleanResult) {
                        // 清洗成功后从Redis移除数据
                        redisTemplate.delete(key);
                        successCount++;
                        log.debug("成功处理并移除Redis键：{}", key);
                    } else {
                        log.warn("数据清洗失败，保留Redis键：{}", key);
                    }

                    processedCount++;
                } catch (Exception e) {
                    log.error("处理Redis键{}时发生异常", key, e);
                }
            }

            log.info("Redis数据处理完成，处理{}个，成功{}个", processedCount, successCount);

        } catch (Exception e) {
            log.error("处理Redis数据时发生异常", e);
        }
    }

    /**
     * 解析JSON数据并进行清洗
     *
     * @param jsonData JSON数据字符串
     * @param redisKey Redis键名
     * @return 是否清洗成功
     */
    private boolean parseAndCleanData(String jsonData, String redisKey) {
        try {
            // 解析JSON数据
            JSONObject jsonObject = JSON.parseObject(jsonData);

            // 检查是否有data数组
            if (!jsonObject.containsKey("data")) {
                log.warn("JSON数据中没有data字段，Redis键：{}", redisKey);
                return false;
            }

            JSONArray dataArray = jsonObject.getJSONArray("data");
            if (dataArray == null || dataArray.isEmpty()) {
                log.debug("data数组为空，Redis键：{}", redisKey);
                return true; // 空数据也算处理成功
            }

            boolean allSuccess = true;
            for (int i = 0; i < dataArray.size(); i++) {
                try {
                    JSONObject dataItem = dataArray.getJSONObject(i);

                    // 将JSON对象转换为PointData实体
                    PointData pointData = dataItem.toJavaObject(PointData.class);

                    // 进行数据清洗
                    Boolean cleanResult = dataCleanService.cleanData(pointData);

                    if (!cleanResult) {
                        allSuccess = false;
                        log.warn("数据清洗失败，数据项：{}", dataItem.toJSONString());
                    }
                } catch (Exception e) {
                    allSuccess = false;
                    log.error("处理数据项时发生异常，索引：{}", i, e);
                }
            }

            return allSuccess;

        } catch (Exception e) {
            log.error("解析JSON数据失败，Redis键：{}", redisKey, e);
            return false;
        }
    }

}
