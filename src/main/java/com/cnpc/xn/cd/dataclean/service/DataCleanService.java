package com.cnpc.xn.cd.dataclean.service;

import cn.hutool.http.HttpUtil;
import com.alibaba.fastjson.JSONObject;
import com.baomidou.mybatisplus.core.conditions.query.QueryWrapper;
import com.baomidou.mybatisplus.extension.service.impl.ServiceImpl;
import com.cnpc.xn.cd.dataclean.entity.PointConfig;
import com.cnpc.xn.cd.dataclean.entity.PointData;
import com.cnpc.xn.cd.dataclean.mapper.PointConfigMapper;
import com.cnpc.xn.cd.dataclean.mapper.PointDataMapper;
import com.cnpc.xn.cd.dataclean.config.DataCollectionConfig;
import lombok.extern.slf4j.Slf4j;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.beans.factory.annotation.Value;
import org.springframework.data.redis.core.RedisTemplate;
import org.springframework.stereotype.Service;
import org.springframework.util.StringUtils;

import java.util.HashMap;
import java.util.Map;
import java.util.concurrent.ScheduledExecutorService;

/**
 * PackName: com.cnpc.xn.cd.dataclean.service
 * ClassName: DataCleanService
 * Author:  zzl.F3
 * Date:     2025/5/26 17:31
 * Description: 数据清洗服务
 */
@Service
@Slf4j
public class DataCleanService extends ServiceImpl<PointDataMapper, PointData> {
    @Autowired
    private PointDataMapper pointDataMapper;
    @Autowired
    private PointConfigMapper pointConfigMapper;

    @Autowired
    private RedisTemplate<String, String> redisTemplate;

    @Autowired
    private DataCollectionConfig collectionConfig;


    // 线程池实例变量，用于管理调度任务
    private ScheduledExecutorService scheduler;
    private ScheduledExecutorService cleanScheduler;

    @Value("${data-clean.pSpace-api.url}")
    private String url;
    @Value("${data-clean.query.method}")
    private String method;

    private String tags;

    @Value("${data-clean.query.thirdAppId}")
    private String thirdAppId;

    @Value("${data-clean.query.licenseCode}")
    private String licenseCode;

    /**
     * 查询数据
     *
     * @param tags 查询标签
     * @return 查询结果
     */
    public String queryData(String tags) {
        Map<String, String> headers = new HashMap<>();
        headers.put("Content-Type", "application/x-www-form-urlencoded");
        String resMsg = "";
        resMsg = HttpUtil.createPost(url).headerMap(headers, true) // 设置请求头
                .form("method", method) // 设置表单参数
                .form("tags", getTags()).form("thirdAppld", thirdAppId).form("licenseCode", licenseCode).execute() // 执行请求
                .body(); // 获取响应内容
        log.info("Tags:{};数据查询成功:{}", tags, resMsg);
        return resMsg;
    }

    /**
     * 手动保存数据
     *
     * @param json 数据
     */
    public void manualSaveData(String json){
        PointData pointData = JSONObject.parseObject(json, PointData.class);
        cleanData(pointData);
    }


    /**
     * 数据清洗方法
     * 根据pointData中的STREXT2字段去PointConfig表中查询到data_type，
     * 根据data_type按不同逻辑处理pointData中的value字段
     *
     * @param pointData 点位数据
     * @return 清洗后的点位数据
     */
    public Boolean cleanData(PointData pointData) {
        if (pointData == null || !StringUtils.hasText(pointData.getStrExt2())) {
            log.warn("点位数据为空或STREXT2字段为空，无法进行数据清洗");
            return false;
        }

        try {
            // 1. 获取数据类型
            String id = pointData.getStrExt2();
            String dataType = pointConfigMapper.selectById(id).getDataType();
            if (!StringUtils.hasText(dataType)) {
                log.warn("未找到STREXT2={}对应的data_type配置", pointData.getStrExt2());
                return false;
            }

            log.info("开始清洗数据，STREXT2={}, data_type={}, 原始值={}", pointData.getStrExt2(), dataType, pointData.getValue());

            // 2. 保存原始值
            Double originalValue = pointData.getValue();
            if (originalValue == null) {
                log.warn("当前值为空，无法进行数据清洗");
                return false;
            }

            // 3. 根据data_type应用不同的清洗规则
            Double cleanedValue = applyCleaningRules(pointData, dataType);

            // 4. 更新清洗后的值
            pointData.setValue(cleanedValue);

            // 5. 保存数据
            pointDataMapper.insert(pointData);
            log.info("数据清洗完成，STREXT2={}, 原始值={}, 清洗后值={}", pointData.getStrExt2(), originalValue, cleanedValue);
            return true;

        } catch (Exception e) {
            log.error("数据清洗过程中发生异常，STREXT2={}", pointData.getStrExt2(), e);
            return false;
        }
    }


    /**
     * 根据data_type应用不同的清洗规则
     *
     * @param pointData 点位数据
     * @param dataType  数据类型
     * @return 清洗后的值
     */
    private Double applyCleaningRules(PointData pointData, String dataType) {
        Double currentValue = pointData.getValue();
        // 规则3：出现负值的时候，保留原始负值
        if (currentValue < 0) {
            log.info("检测到负值，保留原始负值，STREXT2={}, 值={}", pointData.getStrExt2(), currentValue);
            return currentValue;
        }

        switch (dataType) {
            case "日发电量":
                return cleanDailyPowerData(pointData, currentValue);
            case "总发电量":
                return cleanTotalPowerData(pointData, currentValue);
            case "压力":
            case "温度":
            case "功率":
            case "辐照度":
                return cleanSpecialTypeData(pointData, currentValue, dataType);
            default:
                log.info("未知的data_type={}，保留原始值", dataType);
                return currentValue;
        }
    }

    /**
     * 清洗日发电量数据
     * 规则1：每天的0-24点 如果出现0或者小于上一秒的值的时候 用前面的值替换
     *
     * @param pointData     点位数据
     * @param originalValue 原始值
     * @return 清洗后的值
     */
    private Double cleanDailyPowerData(PointData pointData, Double originalValue) {
        try {
            // 获取前一条数据
            Double previousValue = getPreviousValue(pointData);
            // 如果出现0值或者小于上一秒的值，用前面的值替换
            if (originalValue == 0.0 || (previousValue != null && originalValue < previousValue)) {
                log.info("日发电量数据清洗：原值={}, 替换为前值={}", originalValue, previousValue);
                return previousValue;
            }
            return originalValue;
        } catch (Exception e) {
            log.error("日发电量数据清洗失败", e);
            return originalValue;
        }
    }

    /**
     * 清洗总发电量数据
     * 规则2：如果出现0值或者小于上一秒的值的时候 用前面的值替换
     *
     * @param pointData     点位数据
     * @param originalValue 原始值
     * @return 清洗后的值
     */
    private Double cleanTotalPowerData(PointData pointData, Double originalValue) {
        try {
            // 获取前一条数据
            Double previousValue = getPreviousValue(pointData);

            // 如果出现0值或者小于上一秒的值，用前面的值替换
            if (originalValue == 0.0 || (previousValue != null && originalValue < previousValue)) {
                log.info("总发电量数据清洗：原值={}, 替换为前值={}", originalValue, previousValue);
                return previousValue;
            }
            return originalValue;
        } catch (Exception e) {
            log.error("总发电量数据清洗失败", e);
            return originalValue;
        }
    }

    /**
     * 清洗特殊类型数据（压力、温度、功率、辐照度）
     * 规则4：突然出现0值，然后又恢复正常，将0值用前面的值替换。出现连续0值，就保留0值
     *
     * @param pointData     点位数据
     * @param originalValue 原始值
     * @param dataType      数据类型
     * @return 清洗后的值
     */
    private Double cleanSpecialTypeData(PointData pointData, Double originalValue, String dataType) {
        try {
            if (originalValue != 0.0) {
                return originalValue;
            }

            // 当前值为0，需要判断是否为突然出现的0值
            Double previousValue = getPreviousValue(pointData);
            // 检查是否为连续0值
            if (previousValue != null && previousValue == 0.0) {
                log.info("{}数据清洗：检测到连续0值，保留原值={}", dataType, originalValue);
                return originalValue;
            }
            // 如果前值不为0，说明可能是突然出现的0值，用前值替换
            else {
                log.info("{}数据清洗：检测到突然出现的0值，原值={}, 替换为前值={}", dataType, originalValue, previousValue);
                return previousValue;
            }
        } catch (Exception e) {
            log.error("{}数据清洗失败", dataType, e);
            return originalValue;
        }
    }

    /**
     * 获取前一条数据的值
     * 根据StrExt2从数据库取最新一条数据返回其value
     *
     * @param pointData 当前点位数据
     * @return 前一条数据的值
     */
    private Double getPreviousValue(PointData pointData) {
        // 根据strExt2查询最新一条数据
        QueryWrapper<PointData> queryWrapper = new QueryWrapper<>();
        queryWrapper.eq("strext2", pointData.getStrExt2()).orderByDesc("create_time").last("LIMIT 1");

        PointData latestData = pointDataMapper.selectOne(queryWrapper);

        if (latestData != null) {
            log.debug("获取到前一条数据，STREXT2={}, 前值={}", pointData.getStrExt2(), latestData.getValue());
            return latestData.getValue();
        } else {
            log.debug("未找到STREXT2={}的历史数据", pointData.getStrExt2());
            return 0.0;
        }
    }

    private String getTags() {
        return "\\" + (tags != null ? tags : "");
    }
}