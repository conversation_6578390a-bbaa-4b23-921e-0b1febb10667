package com.cnpc.xn.cd.dataclean.controller;

import com.cnpc.xn.cd.dataclean.config.DataCollectionConfig;
import com.cnpc.xn.cd.dataclean.service.DataCleanService;
import lombok.extern.slf4j.Slf4j;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Controller;

import javax.annotation.PostConstruct;
import java.util.concurrent.ScheduledExecutorService;
import java.util.concurrent.TimeUnit;

/**
 * 数据清洗控制器
 * Spring容器初始化完成后自动启动数据采集任务
 *
 * <AUTHOR>
 * @date 2025-05-21
 */
@Controller
@Slf4j
public class DataCleanController {

    @Autowired
    private DataCleanService dataCleanService;

    @Autowired
    private DataCollectionConfig collectionConfig;

    /**
     * Spring容器初始化完成后自动启动数据采集任务
     */
    @PostConstruct
    public void initDataCollection() {
        // 检查是否启用自动启动
        if (!collectionConfig.isAutoStart()) {
            log.info("数据采集自动启动已禁用，跳过启动");
            return;
        }

        // 延迟启动，确保所有依赖都已初始化完成
        new Thread(() -> {
            try {
                Thread.sleep(collectionConfig.getStartDelay() * 1000L); // 使用配置的延迟时间
                log.info("项目启动完成，开始自动启动数据采集任务，延迟{}秒", collectionConfig.getStartDelay());
                dataCleanService.saveData();
            } catch (InterruptedException e) {
                log.error("数据采集任务启动被中断", e);
                Thread.currentThread().interrupt();
            } catch (Exception e) {
                log.error("自动启动数据采集任务失败", e);
            }
        }, "DataCollection-Initializer").start();
    }

    /**
     * 停止数据采集任务
     */
    public void stopDataCollection() {
        log.info("手动停止数据采集任务");
        ScheduledExecutorService scheduler = dataCleanService.getScheduler();
        if (scheduler != null && !scheduler.isShutdown()) {
            scheduler.shutdown();
            try {
                if (!scheduler.awaitTermination(30, TimeUnit.SECONDS)) {
                    scheduler.shutdownNow();
                }
                log.info("数据采集任务已停止");
            } catch (InterruptedException e) {
                scheduler.shutdownNow();
                Thread.currentThread().interrupt();
            }
        }
    }


}
