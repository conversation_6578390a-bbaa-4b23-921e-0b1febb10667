package com.cnpc.xn.cd.dataclean.entity;

import com.baomidou.mybatisplus.annotation.IdType;
import com.baomidou.mybatisplus.annotation.TableField;
import com.baomidou.mybatisplus.annotation.TableId;
import com.baomidou.mybatisplus.annotation.TableName;

import com.fasterxml.jackson.annotation.JsonProperty;
import lombok.Data;
import lombok.EqualsAndHashCode;
import lombok.experimental.Accessors;

import java.io.Serializable;
import java.time.LocalDateTime;

/**
 * 点位配置实体类
 *
 * <AUTHOR>
 * @date 2025-05-21
 */
@Data
@TableName("point_config")
public class PointConfig{
    /**
     * id_dw
     */
    @TableField("id")
    @JsonProperty("ID_DW")
    private String id;

    /**
     * 点位类型
     */
    @TableField("point_type")
    @JsonProperty("DW_DL")
    private String pointType;

    /**
     * 站点名称
     */
    @TableField("station_name")
    @JsonProperty("STATION_NAME")
    private String stationName;

    /**
     * 数据类型
     */
    @TableField("data_type")
    @JsonProperty("DATA_TYPE")
    private String dataType;

    /**
     * 设备名称
     */
    @TableField("device_name")
    @JsonProperty("DEVICE_NAME")
    private String deviceName;

    /**
     * 单位
     */
    @TableField("unit")
    @JsonProperty("DW_DW")
    private String unit;
}
