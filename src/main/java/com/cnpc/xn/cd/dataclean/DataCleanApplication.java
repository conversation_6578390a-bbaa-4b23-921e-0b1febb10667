package com.cnpc.xn.cd.dataclean;

import org.mybatis.spring.annotation.MapperScan;
import org.springframework.boot.SpringApplication;
import org.springframework.boot.autoconfigure.SpringBootApplication;
import org.springframework.scheduling.annotation.EnableScheduling;

/**
 * 数据清洗项目启动类
 *
 * <AUTHOR>
 * @date 2025-05-21
 */
@SpringBootApplication(exclude = {
        org.springframework.boot.autoconfigure.jdbc.DataSourceAutoConfiguration.class
})
@MapperScan("com.cnpc.xn.cd.dataclean.mapper")
@EnableScheduling
public class DataCleanApplication {

    public static void main(String[] args) {
        SpringApplication.run(DataCleanApplication.class, args);
        System.out.println("\ndata-clean 数据清洗项目启动成功\n");
    }
}
